import { TiptapTransformer } from '@hocuspocus/transformer';
import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { JSONContent } from '@tiptap/core';
import { IsString } from 'class-validator';
import { v7 as uuidv7 } from 'uuid';
import { Doc } from 'yjs';

import { DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { DraftPage, InsertableDraftPage, UpdatableDraftPage } from '../../../../drizzle/schema/draft-pages';
import { InsertablePublicPage, PublicPage } from '../../../../drizzle/schema/public-pages';
import { DRAFT_PAGE_REPO, PUBLIC_PAGE_REPO } from '../../injects';
import { IDraftPageRepo } from '../repositories/draft-page-repo.interface';
import { IPublicPageRepo } from '../repositories/public-page-repo.interface';

export class CreatePageDto {
    @IsString()
    docId: string;
}

@Injectable()
export class PageService {
    private readonly logger = new Logger(PageService.name);

    constructor(
        @Inject(DRAFT_PAGE_REPO)
        private draftPageRepo: IDraftPageRepo,
        @Inject(PUBLIC_PAGE_REPO)
        private publicPageRepo: IPublicPageRepo,
    ) {}

    /**
     * Найти черновик страницы по ID
     */
    async findDraftById(pageId: string): Promise<DraftPage> {
        const page = await this.draftPageRepo.findById(pageId);
        if (!page) {
            throw new NotFoundException(`Draft page with ID ${pageId} not found`);
        }
        return page;
    }

    /**
     * Поиск существующего черновика страницы по ID
     */
    async findDraftByIdOrNull(pageId: string): Promise<DraftPage | null> {
        try {
            const result = await this.draftPageRepo.findById(pageId);
            if (!result) {
                this.logger.debug(`Черновик страницы не найден для ID: ${pageId}`);
                return null;
            }
            this.logger.debug(`Найден существующий черновик страницы с ID: ${pageId}`);
            return result;
        } catch (error) {
            this.logger.error(error, `Ошибка загрузки черновика страницы (pageId: ${pageId}):`);
            return null;
        }
    }

    /**
     * Получить публичные страницы документа
     */
    async getPublicPagesByDocId(docId: string): Promise<PublicPage[]> {
        return await this.publicPageRepo.findByDocId(docId);
    }

    /**
     * Создать черновик страницы
     */
    async createDraftPage(docId: string, pageId: string, trx?: DrizzleTransaction): Promise<DraftPage> {
        const insertableData: InsertableDraftPage = {
            id: pageId,
            docId,
            ydoc: null,
        };

        try {
            const page = await this.draftPageRepo.insertPage(insertableData, trx);
            this.logger.debug(`Создан новый черновик страницы с ID: ${pageId}`);
            return page;
        } catch (createError) {
            this.logger.error(createError, `Ошибка создания черновика страницы для ${pageId}:`);
            throw createError;
        }
    }

    /**
     * Обновить черновик страницы
     */
    async updateDraftPage(pageId: string, updatePageDto: UpdatableDraftPage): Promise<void> {
        try {
            await this.draftPageRepo.updatePage(updatePageDto, pageId);
            this.logger.debug(`Обновлен черновик страницы с ID: ${pageId}`);
        } catch (createError) {
            this.logger.error(createError, `Ошибка обновления черновика страницы для ${pageId}:`);
            throw createError;
        }
    }

    /**
     * Удалить страницу (опубликованную и черновик)
     */
    async deletePage(pageId: string): Promise<void> {
        this.logger.log(`Удаляем страницу ${pageId}`);

        await this.draftPageRepo.deletePage(pageId);
        await this.publicPageRepo.deletePage(pageId);

        this.logger.log(`Удалена страница ${pageId}`);
    }

    /**
     * Добавить новую страницу к документу (создает только черновик)
     */
    async addPageToDocument(docId: string, trx?: DrizzleTransaction): Promise<DraftPage> {
        const pageId = uuidv7();
        return await this.createDraftPage(docId, pageId, trx);
    }

    /**
     * Опубликовать страницы документа (конвертировать ydoc в content)
     */
    async publishDocumentPages(docId: string): Promise<void> {
        this.logger.log(`Публикуем страницы для документа ${docId}`);

        const draftPages = await this.draftPageRepo.findByDocId(docId);

        if (draftPages.length === 0) {
            this.logger.warn(`Нет черновиков для документа ${docId}`);
            return;
        }

        // Создаем новые публичные страницы
        const publicPages: InsertablePublicPage[] = draftPages.map((draft) => ({
            id: draft.id,
            docId: draft.docId,
            content: draft.ydoc ? (TiptapTransformer.fromYdoc(draft.ydoc as Doc, 'default') as JSONContent) : null,
        }));

        await this.publicPageRepo.insertManyPages(publicPages);
        this.logger.log(`Published ${publicPages.length} pages for document ${docId}`);
    }

    /**
     * Получить черновики страниц по docId (для использования в транзакциях)
     */
    async getDraftPagesByDocId(docId: string, trx?: DrizzleTransaction): Promise<DraftPage[]> {
        return await this.draftPageRepo.findByDocId(docId, trx);
    }
}
