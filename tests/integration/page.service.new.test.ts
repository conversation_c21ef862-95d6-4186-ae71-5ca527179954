import { randomUUID } from 'node:crypto';
import { NotFoundException } from '@nestjs/common';

import { CreateDocumentDto } from '../../src/modules/editor/application/services/document.service';
import { app, documentService, pageService } from './test-setup';

describe('PageService Integration Tests (New Architecture)', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(documentService).toBeDefined();
        expect(pageService).toBeDefined();
    });

    describe('createDraftPage', () => {
        it('должен создать черновик страницы', async () => {
            // Сначала создаем документ
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service',
                entityId: 'test-entity',
                schoolId: 'test-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Создаем дополнительную черновую страницу
            const draftPage = await pageService.createDraftPage(doc.id);

            expect(draftPage).toBeDefined();
            expect(draftPage.id).toBeDefined();
            expect(draftPage.docId).toBe(doc.id);
            expect(draftPage.ydoc).toBeNull();
            expect(draftPage.createdAt).toBeDefined();
            expect(draftPage.updatedAt).toBeDefined();
        });

        it('должен создать черновик страницы с предустановленным pageId', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'custom-service',
                entityId: 'custom-entity',
                schoolId: 'custom-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            const customPageId = randomUUID();
            const draftPage = await pageService.createDraftPage(doc.id, customPageId);

            expect(draftPage).toBeDefined();
            expect(draftPage.id).toBe(customPageId);
            expect(draftPage.docId).toBe(doc.id);
        });
    });

    describe('findDraftById', () => {
        it('должен найти существующий черновик страницы', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'find-service',
                entityId: 'find-entity',
                schoolId: 'find-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Получаем созданную черновую страницу
            const draftPages = await pageService.getDraftPagesByDocId(doc.id);
            expect(draftPages).toHaveLength(1);
            const draftPageId = draftPages[0].id;

            const foundPage = await pageService.findDraftById(draftPageId);

            expect(foundPage.id).toBe(draftPageId);
            expect(foundPage.docId).toBe(doc.id);
        });

        it('должен выбросить NotFoundException для несуществующего черновика', async () => {
            const nonExistentId = randomUUID();

            await expect(pageService.findDraftById(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(pageService.findDraftById(nonExistentId)).rejects.toThrow(
                `Draft page with ID ${nonExistentId} not found`
            );
        });
    });

    describe('findDraftByIdOrNull', () => {
        it('должен найти существующий черновик страницы', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'find-or-null-service',
                entityId: 'find-or-null-entity',
                schoolId: 'find-or-null-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            const draftPages = await pageService.getDraftPagesByDocId(doc.id);
            const draftPageId = draftPages[0].id;

            const foundPage = await pageService.findDraftByIdOrNull(draftPageId);

            expect(foundPage).toBeDefined();
            expect(foundPage.id).toBe(draftPageId);
            expect(foundPage.docId).toBe(doc.id);
        });

        it('должен вернуть null для несуществующего черновика', async () => {
            const nonExistentId = randomUUID();

            const result = await pageService.findDraftByIdOrNull(nonExistentId);

            expect(result).toBeNull();
        });
    });

    describe('addPageToDocument', () => {
        it('должен добавить новую страницу к документу', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'add-page-service',
                entityId: 'add-page-entity',
                schoolId: 'add-page-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Проверяем, что изначально есть одна страница
            let draftPages = await pageService.getDraftPagesByDocId(doc.id);
            expect(draftPages).toHaveLength(1);

            // Добавляем новую страницу
            const newPage = await pageService.addPageToDocument(doc.id);

            expect(newPage).toBeDefined();
            expect(newPage.docId).toBe(doc.id);

            // Проверяем, что теперь есть две страницы
            draftPages = await pageService.getDraftPagesByDocId(doc.id);
            expect(draftPages).toHaveLength(2);
        });
    });

    describe('deletePage', () => {
        it('должен удалить страницу из обеих таблиц', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'delete-page-service',
                entityId: 'delete-page-entity',
                schoolId: 'delete-page-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Добавляем еще одну страницу
            await pageService.addPageToDocument(doc.id);

            // Публикуем документ
            await documentService.publishDocument(doc.id);

            // Проверяем, что есть страницы в обеих таблицах
            let draftPages = await pageService.getDraftPagesByDocId(doc.id);
            let publicPages = await pageService.getPublicPagesByDocId(doc.id);
            expect(draftPages).toHaveLength(2);
            expect(publicPages).toHaveLength(2);

            // Удаляем одну страницу
            const pageToDelete = draftPages[0].id;
            await pageService.deletePage(pageToDelete);

            // Проверяем, что страница удалена из обеих таблиц
            draftPages = await pageService.getDraftPagesByDocId(doc.id);
            publicPages = await pageService.getPublicPagesByDocId(doc.id);
            expect(draftPages).toHaveLength(1);
            expect(publicPages).toHaveLength(1);
            expect(draftPages.find(p => p.id === pageToDelete)).toBeUndefined();
            expect(publicPages.find(p => p.id === pageToDelete)).toBeUndefined();
        });
    });

    describe('publishDocumentPages', () => {
        it('должен опубликовать страницы документа', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'publish-pages-service',
                entityId: 'publish-pages-entity',
                schoolId: 'publish-pages-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Добавляем еще страницы
            await pageService.addPageToDocument(doc.id);
            await pageService.addPageToDocument(doc.id);

            // Проверяем, что есть только черновые страницы
            const draftPages = await pageService.getDraftPagesByDocId(doc.id);
            let publicPages = await pageService.getPublicPagesByDocId(doc.id);
            expect(draftPages).toHaveLength(3);
            expect(publicPages).toHaveLength(0);

            // Публикуем страницы
            await pageService.publishDocumentPages(doc.id);

            // Проверяем, что создались публичные страницы
            publicPages = await pageService.getPublicPagesByDocId(doc.id);
            expect(publicPages).toHaveLength(3);
            expect(publicPages.map(p => p.id).sort()).toEqual(draftPages.map(p => p.id).sort());
        });

        it('должен корректно обработать документ без черновых страниц', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'empty-doc-service',
                entityId: 'empty-doc-entity',
                schoolId: 'empty-doc-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Удаляем все черновые страницы
            const draftPages = await pageService.getDraftPagesByDocId(doc.id);
            for (const page of draftPages) {
                await pageService.deleteManyDraftPages([page.id]);
            }

            // Пытаемся опубликовать документ без черновых страниц
            await expect(pageService.publishDocumentPages(doc.id)).resolves.not.toThrow();

            // Проверяем, что публичных страниц не создалось
            const publicPages = await pageService.getPublicPagesByDocId(doc.id);
            expect(publicPages).toHaveLength(0);
        });
    });

    describe('updateDraftPage', () => {
        it('должен обновить черновик страницы', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'update-service',
                entityId: 'update-entity',
                schoolId: 'update-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            const draftPages = await pageService.getDraftPagesByDocId(doc.id);
            const pageId = draftPages[0].id;

            // Обновляем страницу
            const updateData = {
                ydoc: Buffer.from('test ydoc data'),
            };

            await pageService.updateDraftPage(pageId, updateData);

            // Проверяем, что обновление прошло успешно
            const updatedPage = await pageService.findDraftById(pageId);
            expect(updatedPage.ydoc).toEqual(updateData.ydoc);
        });
    });

    describe('getPublicPagesByDocId', () => {
        it('должен получить публичные страницы документа', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'get-public-service',
                entityId: 'get-public-entity',
                schoolId: 'get-public-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Добавляем страницы и публикуем
            await pageService.addPageToDocument(doc.id);
            await documentService.publishDocument(doc.id);

            const publicPages = await pageService.getPublicPagesByDocId(doc.id);

            expect(publicPages).toHaveLength(2);
            publicPages.forEach(page => {
                expect(page.docId).toBe(doc.id);
                expect(page.id).toBeDefined();
                expect(page.createdAt).toBeDefined();
                expect(page.updatedAt).toBeDefined();
            });
        });

        it('должен вернуть пустой массив для документа без публичных страниц', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'no-public-service',
                entityId: 'no-public-entity',
                schoolId: 'no-public-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            const publicPages = await pageService.getPublicPagesByDocId(doc.id);

            expect(publicPages).toHaveLength(0);
        });
    });

    describe('getDraftPagesByDocId', () => {
        it('должен получить черновые страницы документа', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'get-draft-service',
                entityId: 'get-draft-entity',
                schoolId: 'get-draft-school',
            };
            const doc = await documentService.createDocumentWithDraftPage(createDocDto);

            // Добавляем еще страницы
            await pageService.addPageToDocument(doc.id);
            await pageService.addPageToDocument(doc.id);

            const draftPages = await pageService.getDraftPagesByDocId(doc.id);

            expect(draftPages).toHaveLength(3);
            draftPages.forEach(page => {
                expect(page.docId).toBe(doc.id);
                expect(page.id).toBeDefined();
                expect(page.createdAt).toBeDefined();
                expect(page.updatedAt).toBeDefined();
            });
        });
    });
});
